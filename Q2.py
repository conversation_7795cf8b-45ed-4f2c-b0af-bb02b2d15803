import pandas as pd
from typing import Sequence

def prepare_data(file_name):
    df_words = pd.read_csv(file_name).set_index('Word')
    # split phonemes
    df_all_phonemes = df_words['Phoneme'].str.split(' ', expand=True).stack()
    # count unique phonemes
    df_uniq_phonemes = df_all_phonemes.unique()
    # encode phonemes
    df_uniq_phonemes = dict(zip(df_uniq_phonemes, range(len(df_uniq_phonemes))))
    # set fix length for encodings
    max_len_phonemes = len(str(len(df_uniq_phonemes)))
    df_uniq_phonemes = {k: f'{v:0{max_len_phonemes}d}' for k, v in df_uniq_phonemes.items()}

    #encode phonemes
    df_words['Phoneme'] = df_words['Phoneme'].apply(lambda x: ''.join([df_uniq_phonemes[p] for p in x.split(' ')]))
    return df_words, df_uniq_phonemes

def find_word_combos_with_pronunciation(phonemes: Sequence[str]) -> Sequence[Sequence[str]]:
    df_words, df_uniq_phonemes = prepare_data('cmudict.csv')
    
    #encode phonemes
    phonemes = ''.join([df_uniq_phonemes[p] for p in phonemes])
    
    #find all possible combinations recursively
    def find_combos(phonemes, df_words):
        if len(phonemes) == 0:
            return None
        #find words that phonemes start with
        df_matched_words = df_words[df_words['Phoneme'].apply(lambda x: phonemes.startswith(x))]
        if df_matched_words.empty:
            return None
        all_combos = []
        for index, row in df_matched_words.iterrows():
            if phonemes == row['Phoneme']:
                all_combos.append([index])
            else:
                combos = find_combos(phonemes[len(row['Phoneme']):], df_words)
                if combos:
                    all_combos.extend([[index] + combo for combo in combos])

        
        return all_combos
    
    return find_combos(phonemes, df_words)


if __name__ == '__main__':
    print(find_word_combos_with_pronunciation(["DH", "EH", "R", "DH", "EH", "R"]))
    
    
    
