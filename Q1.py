from typing import Sequence

import numpy as np


def prob_rain_more_than_n(p: Sequence[float], n: int) -> float:

    # check the inputs    
    if n<0:
        raise ValueError(f"n must be greater than 0")
    if len(p)<n:
        raise ValueError(f"invalid number of days")
    
    max_rainy_days = len(p)
    
    # prob_rainy_days[i][j]: probability of exactly i rainy days in the first j days
    prob_rainy_days = np.zeros((max_rainy_days+1, max_rainy_days)) 
    prob_rainy_days[0][0] = 1-p[0]
    prob_rainy_days[1][0] = p[0]
    
    # create the marix of probabilities
    for j in range(1, max_rainy_days):
        for i in range(max_rainy_days+1):
            if i == 0:
                prob_rainy_days[i][j] = prob_rainy_days[i][j-1] * (1-p[j])
            elif i == j+1:
                prob_rainy_days[i][j] = prob_rainy_days[i-1][j-1] * p[j]
            else:
                prob_rainy_days[i][j] = prob_rainy_days[i][j-1] * (1-p[j]) + prob_rainy_days[i-1][j-1] * p[j]
    
    # sum the probabilities of having more than n rainy days    
    return sum(prob_rainy_days[k][-1] for k in range(n, max_rainy_days+1))
        
        
        
if __name__ == "__main__":
    p = [0.15] * 365
    print(prob_rain_more_than_n(p, 365))