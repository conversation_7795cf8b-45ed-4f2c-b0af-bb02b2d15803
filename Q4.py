import numpy as np

def _log_softmax(logits, axis=-1):
    m = np.max(logits, axis=axis, keepdims=True)
    return logits - (m + np.log(np.sum(np.exp(logits - m), axis=axis, keepdims=True)))


def _log_sum_exp(a: np.ndarray, axis=None):
    a_max = np.max(a, axis=axis, keepdims=True)
    with np.errstate(divide='ignore', invalid='ignore'):
        out = a_max + np.log(np.sum(np.exp(a - a_max), axis=axis, keepdims=True))
    if axis is not None:
        out = np.squeeze(out, axis=axis)
    return out

def _forward_pass(l_prime, log_probs, T,blank=0):
    alpha = np.full((T, len(l_prime)), -np.inf)
    alpha[0, 0] = log_probs[0, blank]
    if len(l_prime) > 1:
        alpha[0, 1] = log_probs[0, l_prime[1]]
    
    for t in range(1, T):
        emit = log_probs[t, l_prime] 
        for s in range(len(l_prime)):
            candidates = []

            # stay
            v = alpha[t-1, s]
            if v > -np.inf/2:
                candidates.append(v)

            # move by 1
            if s - 1 >= 0:
                v = alpha[t-1, s-1]
                if v > -np.inf / 2:
                    candidates.append(v)

            # move by 2 
            if s - 2 >= 0:
                cur = l_prime[s]
                prev2 = l_prime[s-2]
                if (cur != blank) and (cur != prev2):
                    v = alpha[t-1, s-2]
                    if v > -np.inf/2:
                        candidates.append(v)

            if candidates:
                alpha[t, s] = _log_sum_exp(np.array(candidates)).item() + emit[s]  
    
    if len(l_prime) == 1:
        loglik = alpha[T-1, 0]
    else:
        loglik = _log_sum_exp(np.array([alpha[T-1, len(l_prime)-2], alpha[T-1, len(l_prime)-1]])).item()
        
    return alpha, loglik

def _backward_pass(l_prime, log_probs, T, blank=0):
    beta = np.full((T, len(l_prime)), -np.inf)
    beta[T-1, -1] = log_probs[T-1, blank]
    if len(l_prime) > 1:
        beta[T-1, -2] = log_probs[T-1, l_prime[-2]]
        
    for t in range(T-2, -1, -1):
        emit = log_probs[t, l_prime]
        for s in range(len(l_prime)):
            candidates = []
            
            # stay
            v = beta[t+1, s]
            if v > -np.inf/2:
                candidates.append(v)
                
            # move by 1
            if s + 1 < len(l_prime):
                v = beta[t+1, s+1]
                if v > -np.inf/2:
                    candidates.append(v)
                    
            # move by 2
            if s + 2 < len(l_prime):
                cur = l_prime[s]
                next2 = l_prime[s+2]
                if (cur != blank) and (cur != next2):
                    v = beta[t+1, s+2]
                    if v > -np.inf/2:
                        candidates.append(v)
                        
            if candidates:
                beta[t, s] = _log_sum_exp(np.array(candidates)).item() + emit[s]
                
    return beta

def ctc_forward_backward(logits, target):
    T, C = logits.shape
    
    # add blanks to target
    blank = 0
    l_prime = np.zeros(2*len(target)+1, dtype=np.int32)
    l_prime[1::2] = target
    print (l_prime)
    
    log_probs = _log_softmax(logits)
    probs = np.exp(log_probs)
    
    #Forward pass
    alpha, loglik = _forward_pass(l_prime, log_probs, T, blank=blank)
    
    #Backward pass
    beta = _backward_pass(l_prime, log_probs, T, blank=blank)
    
    # Compute gradients
    time_idx = np.arange(T)[:, None]
    log_gamma = alpha + beta - loglik - log_probs[time_idx, l_prime]
    dlogits = probs.copy()
    for k in range(C):
        gamma_tk =0
        mask = (l_prime == k)
        if np.any(mask):
            masked = np.where(mask, log_gamma, -np.inf)
            gamma_tk = np.exp(_log_sum_exp(masked, axis=1))
        else:
            gamma_tk= np.zeros(T)
        dlogits[:, k] -= gamma_tk
        
    loss = -loglik
    return loss, dlogits    
    
    return loss, dlogits
    
if __name__ == "__main__":

    logits = np.random.randn(6, 5) * 0.5
    target = [2, 3, 2]

    loss, dlogits = ctc_forward_backward(logits, target)
    print(f"loss = {float(loss):.6f}")